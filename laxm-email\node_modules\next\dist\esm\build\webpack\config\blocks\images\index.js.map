{"version": 3, "sources": ["../../../../../../src/build/webpack/config/blocks/images/index.ts"], "sourcesContent": ["import curry from 'next/dist/compiled/lodash.curry'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { nextImageLoaderRegex } from '../../../../webpack-config'\nimport { loader } from '../../helpers'\nimport { pipe } from '../../utils'\nimport type { ConfigurationContext, ConfigurationFn } from '../../utils'\nimport { getCustomDocumentImageError } from './messages'\n\nexport const images = curry(async function images(\n  _ctx: ConfigurationContext,\n  config: webpack.Configuration\n) {\n  const fns: ConfigurationFn[] = [\n    loader({\n      oneOf: [\n        {\n          test: nextImageLoaderRegex,\n          use: {\n            loader: 'error-loader',\n            options: {\n              reason: getCustomDocumentImageError(),\n            },\n          },\n          issuer: /pages[\\\\/]_document\\./,\n        },\n      ],\n    }),\n  ]\n\n  const fn = pipe(...fns)\n  return fn(config)\n})\n"], "names": ["curry", "nextImageLoaderRegex", "loader", "pipe", "getCustomDocumentImageError", "images", "_ctx", "config", "fns", "oneOf", "test", "use", "options", "reason", "issuer", "fn"], "mappings": "AAAA,OAAOA,WAAW,kCAAiC;AAEnD,SAASC,oBAAoB,QAAQ,6BAA4B;AACjE,SAASC,MAAM,QAAQ,gBAAe;AACtC,SAASC,IAAI,QAAQ,cAAa;AAElC,SAASC,2BAA2B,QAAQ,aAAY;AAExD,OAAO,MAAMC,SAASL,MAAM,eAAeK,OACzCC,IAA0B,EAC1BC,MAA6B;IAE7B,MAAMC,MAAyB;QAC7BN,OAAO;YACLO,OAAO;gBACL;oBACEC,MAAMT;oBACNU,KAAK;wBACHT,QAAQ;wBACRU,SAAS;4BACPC,QAAQT;wBACV;oBACF;oBACAU,QAAQ;gBACV;aACD;QACH;KACD;IAED,MAAMC,KAAKZ,QAAQK;IACnB,OAAOO,GAAGR;AACZ,GAAE", "ignoreList": [0]}