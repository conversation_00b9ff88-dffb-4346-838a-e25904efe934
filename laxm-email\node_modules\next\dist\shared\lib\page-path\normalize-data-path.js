/**
 * strip _next/data/<build-id>/ prefix and .json suffix
 */ "use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "normalizeDataPath", {
    enumerable: true,
    get: function() {
        return normalizeDataPath;
    }
});
function normalizeDataPath(pathname) {
    pathname = pathname.replace(/\/_next\/data\/[^/]{1,}/, '').replace(/\.json$/, '');
    if (pathname === '/index') {
        return '/';
    }
    return pathname;
}

//# sourceMappingURL=normalize-data-path.js.map